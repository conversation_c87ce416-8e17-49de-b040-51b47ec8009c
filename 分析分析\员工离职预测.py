#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
员工离职预测分析
Employee Attrition Prediction Analysis
"""

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np
import os
from sklearn.decomposition import PCA
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier, BaggingClassifier, AdaBoostClassifier, GradientBoostingClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.tree import DecisionTreeClassifier
from imblearn.over_sampling import RandomOverSampler
from collections import Counter
from sklearn.metrics import mean_absolute_error
from sklearn.preprocessing import OneHotEncoder
from sklearn.preprocessing import StandardScaler

# 设置matplotlib后端和中文字体支持
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def load_data():
    """加载数据"""
    try:
        datapath = "employee_leave"
        data = pd.read_csv(os.path.join(datapath, 'train.csv'))
        print("数据加载成功！")
        print(f"数据形状: {data.shape}")
        return data
    except FileNotFoundError:
        print("错误：找不到数据文件，请确保 employee_leave/train.csv 文件存在")
        return None
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None

def data_info(data):
    """显示数据基本信息"""
    print("\n=== 数据基本信息 ===")
    print(data.info())
    print("\n=== 数据前5行 ===")
    print(data.head())
    print("\n=== 各列唯一值数量 ===")
    unique_counts = {x: len(data[x].unique()) for x in data.columns}
    for col, count in unique_counts.items():
        print(f"{col}: {count}")
    return unique_counts

def preprocess_data(data):
    """数据预处理"""
    print("\n=== 开始数据预处理 ===")

    # 1. 删除无用属性
    print("1. 删除无用属性...")
    useless_cols = ['EmployeeCount', 'EmployeeNumber', 'Over18', 'StandardHours']
    train_data1 = data.drop(columns=useless_cols, axis=1)
    print(f"删除后数据形状: {train_data1.shape}")

    # 2. 处理二元分类变量
    print("2. 处理二元分类变量...")
    train_data1['Gender'] = train_data1['Gender'].replace({'Female': 0, 'Male': 1})
    train_data1['OverTime'] = train_data1['OverTime'].replace({'No': 0, 'Yes': 1})

    # 3. 处理序数变量
    print("3. 处理序数变量...")
    train_data1['BusinessTravel'] = train_data1['BusinessTravel'].replace({
        'Non-Travel': 0, 'Travel_Rarely': 1, 'Travel_Frequently': 2
    })

    # 4. One-hot编码处理其他分类变量
    print("4. 进行One-hot编码...")
    categorical_cols = ['Department', 'EducationField', 'JobRole', 'MaritalStatus']

    def encoder(data, column):
        data = data.copy()
        dummies = pd.get_dummies(data[column], prefix=column)
        data = pd.concat([data, dummies], axis=1)
        data = data.drop(column, axis=1)
        return data

    for col in categorical_cols:
        train_data1 = encoder(train_data1, column=col)

    print(f"编码后数据形状: {train_data1.shape}")
    return train_data1

def scale_features(data):
    """特征缩放和数据分割"""
    print("\n=== 特征缩放和数据分割 ===")

    # 分离特征和目标变量
    y = data['Attrition']
    x = data.drop('Attrition', axis=1)

    # 分割训练集和测试集
    x_train, x_test, y_train, y_test = train_test_split(
        x, y, train_size=0.7, shuffle=True, random_state=1
    )

    # 特征缩放
    scaler = StandardScaler()
    scaler.fit(x_train)
    x_train_scaled = pd.DataFrame(
        scaler.transform(x_train),
        index=x_train.index,
        columns=x_train.columns
    )
    x_test_scaled = pd.DataFrame(
        scaler.transform(x_test),
        index=x_test.index,
        columns=x_test.columns
    )

    print(f"训练集形状: {x_train_scaled.shape}")
    print(f"测试集形状: {x_test_scaled.shape}")
    print(f"训练集标签分布: {y_train.value_counts()}")
    print(f"测试集标签分布: {y_test.value_counts()}")

    return x_train_scaled, x_test_scaled, y_train, y_test, scaler

def train_decision_tree(x_train, x_test, y_train, y_test):
    """训练决策树模型"""
    print("\n=== 训练决策树模型 ===")

    # 创建和训练决策树
    dt_model = DecisionTreeClassifier(random_state=42)
    dt_model.fit(x_train, y_train)

    # 预测
    y_pred = dt_model.predict(x_test)

    # 评估
    accuracy = accuracy_score(y_test, y_pred)
    print(f"决策树准确率: {accuracy:.4f}")

    # 详细分类报告
    print("\n分类报告:")
    print(classification_report(y_test, y_pred))

    # 混淆矩阵
    print("\n混淆矩阵:")
    print(confusion_matrix(y_test, y_pred))

    return dt_model, accuracy

def analyze_feature_importance(model, feature_names):
    """分析特征重要性"""
    print("\n=== 特征重要性分析 ===")

    # 获取特征重要性
    importances = model.feature_importances_
    feature_importance = pd.DataFrame({
        'feature': feature_names,
        'importance': importances
    }).sort_values('importance', ascending=False)

    print("前10个最重要的特征:")
    print(feature_importance.head(10))

    # 可视化特征重要性
    plt.figure(figsize=(10, 8))
    top_features = feature_importance.head(15)
    plt.barh(range(len(top_features)), top_features['importance'])
    plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel('特征重要性')
    plt.title('前15个最重要特征')
    plt.gca().invert_yaxis()
    plt.tight_layout()
    plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
    print("特征重要性图已保存为 feature_importance.png")

    return feature_importance

def check_class_imbalance(y_train, y_test):
    """检查类别不平衡问题"""
    print("\n=== 类别不平衡分析 ===")

    train_counts = Counter(y_train)
    test_counts = Counter(y_test)

    print(f"训练集类别分布: {dict(train_counts)}")
    print(f"测试集类别分布: {dict(test_counts)}")

    # 计算不平衡比例
    majority_class = max(train_counts, key=train_counts.get)
    minority_class = min(train_counts, key=train_counts.get)
    imbalance_ratio = train_counts[majority_class] / train_counts[minority_class]

    print(f"不平衡比例: {imbalance_ratio:.2f}:1")

    if imbalance_ratio > 2:
        print("存在类别不平衡问题！")
        return True
    else:
        print("类别分布相对平衡")
        return False

def apply_oversampling(x_train, y_train):
    """应用过采样处理类别不平衡"""
    print("\n=== 应用RandomOverSampler处理类别不平衡 ===")

    print("原始训练集分布:")
    print(Counter(y_train))

    # 应用RandomOverSampler
    ros = RandomOverSampler(random_state=42)
    x_train_resampled, y_train_resampled = ros.fit_resample(x_train, y_train)

    print("过采样后训练集分布:")
    print(Counter(y_train_resampled))

    return x_train_resampled, y_train_resampled

def train_ensemble_models(x_train, x_test, y_train, y_test):
    """训练多种集成学习模型"""
    print("\n=== 训练集成学习模型 ===")

    models = {
        '决策树': DecisionTreeClassifier(random_state=42),
        '随机森林': RandomForestClassifier(n_estimators=100, random_state=42),
        'Bagging': BaggingClassifier(n_estimators=100, random_state=42),
        'AdaBoost': AdaBoostClassifier(n_estimators=100, random_state=42),
        'GradientBoosting': GradientBoostingClassifier(n_estimators=100, random_state=42)
    }

    results = {}

    for name, model in models.items():
        print(f"\n训练 {name} 模型...")
        model.fit(x_train, y_train)
        y_pred = model.predict(x_test)
        accuracy = accuracy_score(y_test, y_pred)
        results[name] = {
            'model': model,
            'accuracy': accuracy,
            'predictions': y_pred
        }
        print(f"{name} 准确率: {accuracy:.4f}")

    # 找到最佳模型
    best_model_name = max(results, key=lambda x: results[x]['accuracy'])
    best_accuracy = results[best_model_name]['accuracy']

    print(f"\n最佳模型: {best_model_name}, 准确率: {best_accuracy:.4f}")

    return results, best_model_name

def compare_with_without_sampling(processed_data):
    """比较采样前后的结果"""
    print("\n=== 比较采样前后的结果 ===")

    # 原始数据
    print("\n--- 原始数据结果 ---")
    x_train, x_test, y_train, y_test, scaler = scale_features(processed_data)

    # 检查类别不平衡
    is_imbalanced = check_class_imbalance(y_train, y_test)

    # 训练原始数据的模型
    original_results, original_best = train_ensemble_models(x_train, x_test, y_train, y_test)

    if is_imbalanced:
        # 过采样数据
        print("\n--- 过采样后结果 ---")
        x_train_resampled, y_train_resampled = apply_oversampling(x_train, y_train)

        # 训练过采样数据的模型
        resampled_results, resampled_best = train_ensemble_models(x_train_resampled, x_test, y_train_resampled, y_test)

        # 比较结果
        print("\n=== 结果对比 ===")
        print(f"原始数据最佳模型: {original_best}, 准确率: {original_results[original_best]['accuracy']:.4f}")
        print(f"过采样后最佳模型: {resampled_best}, 准确率: {resampled_results[resampled_best]['accuracy']:.4f}")

        improvement = resampled_results[resampled_best]['accuracy'] - original_results[original_best]['accuracy']
        print(f"准确率提升: {improvement:.4f}")

        return original_results, resampled_results, x_train, x_test, y_train, y_test
    else:
        return original_results, None, x_train, x_test, y_train, y_test

def main():
    """主函数"""
    print("=== 员工离职预测分析 ===")

    # 1. 加载数据
    data = load_data()
    if data is None:
        return

    # 2. 数据探索
    unique_counts = data_info(data)

    # 3. 数据预处理
    processed_data = preprocess_data(data)

    # 4. 实验课作业内容
    print("\n" + "="*50)
    print("实验课作业分析")
    print("="*50)

    # (1) 分析影响员工离职的主要因素
    print("\n(1) 分析影响员工离职的主要因素")
    x_train, x_test, y_train, y_test, scaler = scale_features(processed_data)
    dt_model, accuracy = train_decision_tree(x_train, x_test, y_train, y_test)
    feature_importance = analyze_feature_importance(dt_model, x_train.columns)

    # (2) 使用多种集成学习方法
    print("\n(2) 使用基础集成、bagging和boosting集成学习方法")
    original_results, resampled_results, x_train, x_test, y_train, y_test = compare_with_without_sampling(processed_data)

    # (3) 类别不平衡处理已在上面完成

    print(f"\n=== 分析完成 ===")
    print("所有结果已保存，特征重要性图已生成")

if __name__ == "__main__":
    main()
