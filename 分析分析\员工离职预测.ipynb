import pandas as pd
import seaborn as sns 
import matplotlib.pyplot as plt
import numpy as np
from sklearn.decomposition import PCA
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import accuracy_score
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import mean_absolute_error
from sklearn.preprocessing import OneHotEncoder
from sklearn.preprocessing import StandardScaler


datapath = "E:\北华航天工业学院\教学工作\人工智能专业\数据挖掘B\数据挖掘B—2023-2024-2\作业、实验\实验四——决策树实验\employee_leave"
data = pd.read_csv(datapath + '/train.csv')
data


data.info()

train_data = data

# count the number of unique values by attribute to identify no use attributes
{x : len(train_data[x].unique()) for x in train_data.columns}

### 删除没有用的属性

train_data1 = train_data.drop(columns=['EmployeeCount','EmployeeNumber','Over18','StandardHours'],axis=1)
train_data1.info()

print("action: identify the categorial (i.e. object) attributes")

{x: len(train_data1[x].unique()) for x in train_data1.select_dtypes('object').columns}

print(train_data1['Gender'].unique())
print(train_data1['OverTime'].unique())

train_data1['Gender'] = train_data1['Gender'].replace({'Female':0,'Male':1})
train_data1['OverTime'] = train_data1['OverTime'].replace({'No':0,'Yes':1})
train_data1

print("action: identify the categorial (i.e. object) attributes")

{x: len(train_data1[x].unique()) for x in train_data1.select_dtypes('object').columns}

print(train_data1['BusinessTravel'].unique())

train_data1['BusinessTravel'] = train_data1['BusinessTravel'].replace({'Non-Travel':0,'Travel_Rarely':1,'Travel_Frequently':2})
train_data1

print("action: take a look at the remaining categorial (i.e. object) attributes")

{x: len(train_data1[x].unique()) for x in train_data1.select_dtypes('object').columns}

print("take a look at the remaining categorial (i.e. object) attributes")

train_data1.select_dtypes('object')

# encoding for remaining categorical attributes

# categorical = train_data1.filter(['Department', 'EducationField', 'JobRole', 'MaritalStatus'])
# numerical = train_data1.drop(['Department', 'EducationField', 'JobRole', 'MaritalStatus'])

# target = train_data1.filter('Attrition',axis=1)
# cat_numerical = pd.get_dummies(categorical,drop_first=True)
# numerical = train_data1.drop('Attrition',axis=1)

def encoder(data,column):
    data = data.copy()
    dummies = pd.get_dummies(data[column], prefix=column)
    data = pd.concat([data, dummies], axis=1)
    data = data.drop(column, axis=1)
    
    return data

for x in ['Department', 'EducationField', 'JobRole', 'MaritalStatus']:
    train_data1 = encoder (train_data1, column=x)



train_data1

# define a function to make a copy of the training dataset

def scale_features(data):
    # step1:
    # split the DataFrame into x-variable(i.e. features) and y-variable(i.e. predictor)
    y = data['Attrition']
    x = data.drop('Attrition',axis=1)

    # split the DataFrame into train and test datasets
    x_train,x_test,y_train,y_test = train_test_split(x,y,train_size = 0.7,shuffle=True,random_state=1)

    # step2:
    # scale features
    scaler = StandardScaler()
    scaler.fit(x_train)
    x_train = pd.DataFrame(scaler.transform(x_train),index=x_train.index,columns=x_train.columns)
    x_test = pd.DataFrame(scaler.transform(x_test),index = x_test.index,columns=x_test.columns)

    return x_train,x_test,y_train,y_test



x_train,x_test,y_train,y_test = scale_features(train_data1)
x_train

DecisionTree = DecisionTreeClassifier()
DecisionTree.fit(x_train, y_train)

y_pred = DecisionTree.predict(x_test)

print(accuracy_score(y_test, y_pred) )

