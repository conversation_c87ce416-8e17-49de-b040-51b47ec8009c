import pandas as pd
import seaborn as sns 
import matplotlib.pyplot as plt
import numpy as np
from sklearn.decomposition import PCA
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import accuracy_score
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import mean_absolute_error
from sklearn.preprocessing import OneHotEncoder
from sklearn.preprocessing import StandardScaler


datapath = "D:\QQ2\分类分析\分析分析\employee_leave"
data = pd.read_csv(datapath + '/train.csv')
data


data.info()

train_data = data

# count the number of unique values by attribute to identify no use attributes
{x : len(train_data[x].unique()) for x in train_data.columns}

### 删除没有用的属性

train_data1 = train_data.drop(columns=['EmployeeCount','EmployeeNumber','Over18','StandardHours'],axis=1)
train_data1.info()

print("action: identify the categorial (i.e. object) attributes")

{x: len(train_data1[x].unique()) for x in train_data1.select_dtypes('object').columns}

print(train_data1['Gender'].unique())
print(train_data1['OverTime'].unique())

train_data1['Gender'] = train_data1['Gender'].replace({'Female':0,'Male':1})
train_data1['OverTime'] = train_data1['OverTime'].replace({'No':0,'Yes':1})
train_data1

print("action: identify the categorial (i.e. object) attributes")

{x: len(train_data1[x].unique()) for x in train_data1.select_dtypes('object').columns}

print(train_data1['BusinessTravel'].unique())

train_data1['BusinessTravel'] = train_data1['BusinessTravel'].replace({'Non-Travel':0,'Travel_Rarely':1,'Travel_Frequently':2})
train_data1

print("action: take a look at the remaining categorial (i.e. object) attributes")

{x: len(train_data1[x].unique()) for x in train_data1.select_dtypes('object').columns}

print("take a look at the remaining categorial (i.e. object) attributes")

train_data1.select_dtypes('object')

# encoding for remaining categorical attributes

# categorical = train_data1.filter(['Department', 'EducationField', 'JobRole', 'MaritalStatus'])
# numerical = train_data1.drop(['Department', 'EducationField', 'JobRole', 'MaritalStatus'])

# target = train_data1.filter('Attrition',axis=1)
# cat_numerical = pd.get_dummies(categorical,drop_first=True)
# numerical = train_data1.drop('Attrition',axis=1)

def encoder(data,column):
    data = data.copy()
    dummies = pd.get_dummies(data[column], prefix=column)
    data = pd.concat([data, dummies], axis=1)
    data = data.drop(column, axis=1)
    
    return data

for x in ['Department', 'EducationField', 'JobRole', 'MaritalStatus']:
    train_data1 = encoder (train_data1, column=x)



train_data1

# define a function to make a copy of the training dataset

def scale_features(data):
    # step1:
    # split the DataFrame into x-variable(i.e. features) and y-variable(i.e. predictor)
    y = data['Attrition']
    x = data.drop('Attrition',axis=1)

    # split the DataFrame into train and test datasets
    x_train,x_test,y_train,y_test = train_test_split(x,y,train_size = 0.7,shuffle=True,random_state=1)

    # step2:
    # scale features
    scaler = StandardScaler()
    scaler.fit(x_train)
    x_train = pd.DataFrame(scaler.transform(x_train),index=x_train.index,columns=x_train.columns)
    x_test = pd.DataFrame(scaler.transform(x_test),index = x_test.index,columns=x_test.columns)

    return x_train,x_test,y_train,y_test



x_train,x_test,y_train,y_test = scale_features(train_data1)
x_train

DecisionTree = DecisionTreeClassifier()
DecisionTree.fit(x_train, y_train)

y_pred = DecisionTree.predict(x_test)

print(accuracy_score(y_test, y_pred) )



# 分析特征重要性
def analyze_attrition_factors(model, feature_names):
    """分析影响员工离职的主要因素"""
    print("=== 影响员工离职的主要因素分析 ===")
    
    # 获取特征重要性
    importances = model.feature_importances_
    feature_importance = pd.DataFrame({
        'feature': feature_names,
        'importance': importances
    }).sort_values('importance', ascending=False)
    
    print("\n前10个最重要的影响因素:")
    top_10 = feature_importance.head(10)
    for i, (idx, row) in enumerate(top_10.iterrows(), 1):
        print(f"{i:2d}. {row['feature']:25s} - 重要性: {row['importance']:.4f}")
    
    # 可视化特征重要性
    plt.figure(figsize=(12, 8))
    top_features = feature_importance.head(15)
    plt.barh(range(len(top_features)), top_features['importance'])
    plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel('特征重要性')
    plt.title('影响员工离职的前15个重要因素')
    plt.gca().invert_yaxis()
    plt.tight_layout()
    plt.show()
    
    return feature_importance

# 使用决策树分析特征重要性
feature_importance = analyze_attrition_factors(DecisionTree, x_train.columns)

def train_ensemble_models(x_train, x_test, y_train, y_test):
    """训练多种集成学习模型"""
    print("=== 集成学习模型比较 ===")
    
    # 定义模型
    models = {
        '决策树 (基础模型)': DecisionTreeClassifier(random_state=42),
        '随机森林 (Bagging)': RandomForestClassifier(n_estimators=100, random_state=42),
        'Bagging分类器': BaggingClassifier(n_estimators=100, random_state=42),
        'AdaBoost (Boosting)': AdaBoostClassifier(n_estimators=100, random_state=42),
        'GradientBoosting': GradientBoostingClassifier(n_estimators=100, random_state=42)
    }
    
    results = {}
    
    print("\n训练各种模型...")
    for name, model in models.items():
        print(f"\n训练 {name}...")
        
        # 训练模型
        model.fit(x_train, y_train)
        
        # 预测
        y_pred = model.predict(x_test)
        
        # 计算准确率
        accuracy = accuracy_score(y_test, y_pred)
        
        # 保存结果
        results[name] = {
            'model': model,
            'accuracy': accuracy,
            'predictions': y_pred
        }
        
        print(f"{name} 准确率: {accuracy:.4f}")
        
        # 显示详细分类报告
        print(f"\n{name} 分类报告:")
        print(classification_report(y_test, y_pred))
    
    # 找到最佳模型
    best_model_name = max(results, key=lambda x: results[x]['accuracy'])
    best_accuracy = results[best_model_name]['accuracy']
    
    print(f"\n=== 模型性能总结 ===")
    print("模型性能排序:")
    sorted_results = sorted(results.items(), key=lambda x: x[1]['accuracy'], reverse=True)
    for i, (name, result) in enumerate(sorted_results, 1):
        print(f"{i}. {name:20s} - 准确率: {result['accuracy']:.4f}")
    
    print(f"\n最佳模型: {best_model_name}")
    print(f"最佳准确率: {best_accuracy:.4f}")
    
    return results, best_model_name

# 训练集成模型
ensemble_results, best_model = train_ensemble_models(x_train, x_test, y_train, y_test)

def check_class_imbalance(y_train, y_test):
    """检查类别不平衡问题"""
    print("=== 类别不平衡分析 ===")
    
    # 统计各类别数量
    train_counts = Counter(y_train)
    test_counts = Counter(y_test)
    
    print(f"训练集类别分布:")
    for label, count in train_counts.items():
        percentage = count / len(y_train) * 100
        print(f"  类别 {label}: {count} 个样本 ({percentage:.1f}%)")
    
    print(f"\n测试集类别分布:")
    for label, count in test_counts.items():
        percentage = count / len(y_test) * 100
        print(f"  类别 {label}: {count} 个样本 ({percentage:.1f}%)")
    
    # 计算不平衡比例
    majority_class = max(train_counts, key=train_counts.get)
    minority_class = min(train_counts, key=train_counts.get)
    imbalance_ratio = train_counts[majority_class] / train_counts[minority_class]
    
    print(f"\n不平衡比例: {imbalance_ratio:.2f}:1")
    print(f"多数类 (类别 {majority_class}): {train_counts[majority_class]} 个样本")
    print(f"少数类 (类别 {minority_class}): {train_counts[minority_class]} 个样本")
    
    # 判断是否存在类别不平衡
    if imbalance_ratio > 2:
        print(f"\n⚠️  存在类别不平衡问题！(比例 > 2:1)")
        return True
    else:
        print(f"\n✅ 类别分布相对平衡 (比例 <= 2:1)")
        return False

# 检查类别不平衡
is_imbalanced = check_class_imbalance(y_train, y_test)

# 如果存在类别不平衡，使用RandomOverSampler处理
if is_imbalanced:
    print("\n=== 使用RandomOverSampler处理类别不平衡 ===")
    
    # 应用RandomOverSampler
    ros = RandomOverSampler(random_state=42)
    x_train_resampled, y_train_resampled = ros.fit_resample(x_train, y_train)
    
    print("\n过采样前训练集分布:")
    original_counts = Counter(y_train)
    for label, count in original_counts.items():
        percentage = count / len(y_train) * 100
        print(f"  类别 {label}: {count} 个样本 ({percentage:.1f}%)")
    
    print("\n过采样后训练集分布:")
    resampled_counts = Counter(y_train_resampled)
    for label, count in resampled_counts.items():
        percentage = count / len(y_train_resampled) * 100
        print(f"  类别 {label}: {count} 个样本 ({percentage:.1f}%)")
    
    print(f"\n训练集大小变化: {len(y_train)} -> {len(y_train_resampled)}")
    
else:
    print("\n数据集类别分布相对平衡，无需进行过采样处理")
    x_train_resampled, y_train_resampled = x_train, y_train

# 比较过采样前后的模型性能
def compare_before_after_sampling(x_train_orig, x_train_resampled, x_test, y_train_orig, y_train_resampled, y_test):
    """比较过采样前后的模型性能"""
    print("=== 过采样前后模型性能比较 ===")
    
    # 选择几个代表性模型进行比较
    models_to_compare = {
        '随机森林': RandomForestClassifier(n_estimators=100, random_state=42),
        'AdaBoost': AdaBoostClassifier(n_estimators=100, random_state=42),
        'GradientBoosting': GradientBoostingClassifier(n_estimators=100, random_state=42)
    }
    
    comparison_results = {}
    
    for model_name, model_class in models_to_compare.items():
        print(f"\n--- {model_name} 模型比较 ---")
        
        # 原始数据训练
        model_orig = model_class
        model_orig.fit(x_train_orig, y_train_orig)
        y_pred_orig = model_orig.predict(x_test)
        accuracy_orig = accuracy_score(y_test, y_pred_orig)
        
        # 过采样数据训练
        model_resampled = model_class.__class__(**model_class.get_params())
        model_resampled.fit(x_train_resampled, y_train_resampled)
        y_pred_resampled = model_resampled.predict(x_test)
        accuracy_resampled = accuracy_score(y_test, y_pred_resampled)
        
        # 计算改进
        improvement = accuracy_resampled - accuracy_orig
        
        comparison_results[model_name] = {
            'original_accuracy': accuracy_orig,
            'resampled_accuracy': accuracy_resampled,
            'improvement': improvement
        }
        
        print(f"原始数据准确率:     {accuracy_orig:.4f}")
        print(f"过采样后准确率:     {accuracy_resampled:.4f}")
        print(f"准确率变化:         {improvement:+.4f}")
        
        if improvement > 0:
            print(f"✅ 过采样提升了模型性能")
        elif improvement < 0:
            print(f"⚠️  过采样降低了模型性能")
        else:
            print(f"➖ 过采样对模型性能无影响")
    
    # 总结
    print(f"\n=== 总结 ===")
    avg_improvement = np.mean([result['improvement'] for result in comparison_results.values()])
    print(f"平均准确率变化: {avg_improvement:+.4f}")
    
    if avg_improvement > 0:
        print("✅ 总体而言，RandomOverSampler提升了模型性能")
    elif avg_improvement < 0:
        print("⚠️  总体而言，RandomOverSampler降低了模型性能")
    else:
        print("➖ RandomOverSampler对模型性能无明显影响")
    
    return comparison_results

# 进行比较
if is_imbalanced:
    comparison_results = compare_before_after_sampling(
        x_train, x_train_resampled, x_test, 
        y_train, y_train_resampled, y_test
    )
else:
    print("\n由于数据集类别平衡，无需进行过采样前后的比较")

